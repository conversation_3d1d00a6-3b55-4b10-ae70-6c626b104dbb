h1. Baby Moderator API Documentation

h2. Project Overview

Baby Moderator is an application designed to monitor and manage user statuses within the XRM system. It automatically changes user statuses from manual mode to outgoing call mode after a specified period and tracks these changes for statistical purposes.

h2. API Endpoints

{expand:title=Authentication}
||Endpoint||Method||Description||
|/baby-moderator/api/login|POST|Authenticates a user and returns a JWT token|
|/baby-moderator/api/verify|GET|Verifies if a JWT token is valid|

h4. Login Request
{code:json}
{
  "username": "string",
  "password": "string"
}
{code}

h4. Login Response
{code:json}
{
  "success": true,
  "token": "JWT_TOKEN_STRING"
}
{code}
{expand}

{expand:title=Settings Management}
||Endpoint||Method||Description||
|/baby-moderator/api/dashboard|GET|Retrieves current dashboard settings|
|/baby-moderator/api/shifts|POST|Updates shift monitoring settings|
|/baby-moderator/api/time|POST|Updates custom time interval settings|
|/baby-moderator/api/audience|POST|Updates the list of target users to monitor|

h4. Shift Settings Request
{code:json}
{
  "startShift": 0/1,
  "endShift": 0/1,
  "loadEnabled": 0/1
}
{code}

h4. Time Settings Request
{code:json}
{
  "timeFrom": "HH:MM",
  "timeTo": "HH:MM"
}
{code}

h4. Target Audience Request
{code:json}
{
  "targetAudience": "username1;username2;username3"
}
{code}
{expand}

{expand:title=Statistics}
||Endpoint||Method||Description||
|/baby-moderator/api/monitoring-stats|GET|Retrieves monitoring statistics for a specific date or today|

h4. Monitoring Stats Response
{code:json}
{
  "daily": {
    "YYYY-MM-DD": {
      "username1": 5,
      "username2": 3
    }
  },
  "allTime": {
    "username1": 42,
    "username2": 27
  }
}
{code}
{expand}

{expand:title=System Status}
||Endpoint||Method||Description||
|/baby-moderator/api/test|GET|Tests application and database connectivity|

h4. Test Response
{code:json}
{
  "application": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO datetime"
  },
  "database": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO datetime"
  }
}
{code}
{expand}

h2. Database Structure

The application uses a MariaDB database with the following tables:

{expand:title=Settings Table}
||Column||Type||Description||
|id|int(11)|Primary key, auto increment|
|startShift|int(11)|Monitor shift start (0=off, 1=on)|
|endShift|int(11)|Monitor shift end (0=off, 1=on)|
|loadEnabled|int(11)|Monitor by custom interval (0=off, 1=on)|
|timeFrom|text|Custom time monitoring, start time|
|timeTo|text|Custom time monitoring, end time|
|targetAudience|longtext|List of agents to monitor|
{expand}

{expand:title=Daily Statistics Table}
||Column||Type||Description||
|id|int(11)|Primary key, auto increment|
|username|varchar(255)|Username being monitored|
|date|date|Date of monitoring|
|changes_count|int(11)|Number of status changes|
|created_at|timestamp|Record creation timestamp|
{expand}

{expand:title=Total Statistics Table}
||Column||Type||Description||
|id|int(11)|Primary key, auto increment|
|username|varchar(255)|Username being monitored|
|total_changes|int(11)|Total number of status changes|
|updated_at|timestamp|Last update timestamp|
{expand}

{expand:title=Users Table}
||Column||Type||Description||
|id|int(11)|Primary key, auto increment|
|login|varchar(150)|User login|
|password|varchar(500)|Encrypted password|
{expand}

h2. External API Interactions

The application interacts with the Kyivstar XRM Gateway API for monitoring and managing user statuses:

h3. XRM API Endpoints Used

||Endpoint||Method||Purpose||
|https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall|GET|Search for user ID by username|
|https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId|GET|Get the current status of a user|
|https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck|POST|Change a user's status|

h3. XRM Status Change Request Example

{code:language=json|title=XRM Status Change Request}
{
  "StatusId": "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
  "UserId": "user-uuid",
  "RoleId": "7e2984c7-430b-4b57-abd1-e7976933c7ad",
  "ip": "Auto",
  "os": "Майкрософт Windows 10 Корпоративная LTSC"
}
{code}

h2. Monitoring Logic

The application implements the following monitoring logic:

# Fetches the target user list from the database
# Monitors users based on configured time settings:
## Start of shift (9:00-9:30)
## End of shift (17:25-17:55)
## Custom time interval
# Groups users for processing (4 users at a time)
# For each user:
## Gets user ID via XRM API
## Checks current status via XRM API
## If user is in "Исх - Ручной режим" for more than 60 seconds:
### Changes status to "Исходящий обзвон"
### Updates statistics in database
# Keeps track of online and offline users
# Checks offline users every 60 seconds to detect when they come back online

h2. System Requirements

* Node.js
* MariaDB database
* Network access to XRM Gateway API
* Environment variables configuration (.env file)

h2. Deployment Instructions

# Clone the repository
# Install dependencies: {noformat}npm install{noformat}
# Configure environment variables in .env file
# Start the server: {noformat}node server.js{noformat}

----

h1. Документація API Baby Moderator

h2. Огляд проекту

Baby Moderator - це додаток, розроблений для моніторингу та управління статусами користувачів у системі XRM. Він автоматично змінює статуси користувачів з ручного режиму на режим вихідного дзвінка після визначеного періоду та відстежує ці зміни для статистичних цілей.

h2. Ендпоінти API

{expand:title=Аутентифікація}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/login|POST|Аутентифікує користувача та повертає JWT токен|
|/baby-moderator/api/verify|GET|Перевіряє валідність JWT токена|

h4. Запит на авторизацію
{code:json}
{
  "username": "string",
  "password": "string"
}
{code}

h4. Відповідь на авторизацію
{code:json}
{
  "success": true,
  "token": "JWT_TOKEN_STRING"
}
{code}
{expand}

{expand:title=Управління налаштуваннями}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/dashboard|GET|Отримує поточні налаштування панелі управління|
|/baby-moderator/api/shifts|POST|Оновлює налаштування моніторингу змін|
|/baby-moderator/api/time|POST|Оновлює налаштування користувацького часового інтервалу|
|/baby-moderator/api/audience|POST|Оновлює список цільових користувачів для моніторингу|

h4. Запит налаштувань змін
{code:json}
{
  "startShift": 0/1,
  "endShift": 0/1,
  "loadEnabled": 0/1
}
{code}

h4. Запит налаштувань часу
{code:json}
{
  "timeFrom": "ГГ:ХХ",
  "timeTo": "ГГ:ХХ"
}
{code}

h4. Запит цільової аудиторії
{code:json}
{
  "targetAudience": "username1;username2;username3"
}
{code}
{expand}

{expand:title=Статистика}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/monitoring-stats|GET|Отримує статистику моніторингу за конкретну дату або сьогодні|

h4. Відповідь статистики моніторингу
{code:json}
{
  "daily": {
    "РРРР-ММ-ДД": {
      "username1": 5,
      "username2": 3
    }
  },
  "allTime": {
    "username1": 42,
    "username2": 27
  }
}
{code}
{expand}

{expand:title=Стан системи}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/test|GET|Тестує підключення додатку та бази даних|

h4. Відповідь тесту
{code:json}
{
  "application": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  },
  "database": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  }
}
{code}
{expand}

h2. Структура бази даних

Додаток використовує базу даних MariaDB з наступними таблицями:

{expand:title=Таблиця налаштувань (Settings)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|startShift|int(11)|Моніторинг початку зміни (0=вимк, 1=увімк)|
|endShift|int(11)|Моніторинг кінця зміни (0=вимк, 1=увімк)|
|loadEnabled|int(11)|Моніторинг за інтервалом (0=вимк, 1=увімк)|
|timeFrom|text|Моніторинг плаваючого часу, початок інтервалу|
|timeTo|text|Моніторинг плаваючого часу, кінець інтервалу|
|targetAudience|longtext|Список агентів для моніторингу|
{expand}

{expand:title=Таблиця щоденної статистики (Daily Statistics)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|username|varchar(255)|Ім'я користувача для моніторингу|
|date|date|Дата моніторингу|
|changes_count|int(11)|Кількість змін статусу|
|created_at|timestamp|Час створення запису|
{expand}

{expand:title=Таблиця загальної статистики (Total Statistics)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|username|varchar(255)|Ім'я користувача для моніторингу|
|total_changes|int(11)|Загальна кількість змін статусу|
|updated_at|timestamp|Час останнього оновлення|
{expand}

{expand:title=Таблиця користувачів (Users)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|login|varchar(150)|Логін користувача|
|password|varchar(500)|Зашифрований пароль|
{expand}

h2. Взаємодія із зовнішніми API

Додаток взаємодіє з Kyivstar XRM Gateway API для моніторингу та управління статусами користувачів:

h3. Ендпоінти XRM API, що використовуються

||Ендпоінт||Метод||Призначення||
|https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall|GET|Пошук ID користувача за ім'ям користувача|
|https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId|GET|Отримання поточного статусу користувача|
|https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck|POST|Зміна статусу користувача|

h3. Приклад запиту на зміну статусу XRM

{code:language=json|title=Запит зміни статусу}
{
  "StatusId": "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
  "UserId": "user-uuid",
  "RoleId": "7e2984c7-430b-4b57-abd1-e7976933c7ad",
  "ip": "Auto",
  "os": "Майкрософт Windows 10 Корпоративная LTSC"
}
{code}

h2. Логіка моніторингу

Додаток реалізує наступну логіку моніторингу:

# Отримує список цільових користувачів з бази даних
# Моніторить користувачів на основі налаштованих часових параметрів:
## Початок зміни (9:00-9:30)
## Кінець зміни (17:25-17:55)
## Користувацький часовий інтервал
# Групує користувачів для обробки (по 4 користувачі за раз)
# Для кожного користувача:
## Отримує ID користувача через XRM API
## Перевіряє поточний статус через XRM API
## Якщо користувач у статусі "Исх - Ручной режим" більше 60 секунд:
### Змінює статус на "Исходящий обзвон"
### Оновлює статистику в базі даних
# Відстежує онлайн та офлайн користувачів
# Перевіряє офлайн користувачів кожні 60 секунд, щоб виявити, коли вони повернуться в онлайн

h2. Системні вимоги

* Node.js
* База даних MariaDB
* Доступ до мережі для роботи з XRM Gateway API
* Налаштування змінних середовища (.env файл)

h2. Інструкції з розгортання

# Клонувати репозиторій
# Встановити залежності: {noformat}npm install{noformat}
# Налаштувати змінні середовища в .env файлі
# Запустити сервер: {noformat}node server.js{noformat}
