let timeoutId;

document.addEventListener('DOMContentLoaded', async () => {
    if (window.location.pathname.includes('dashboard')) {
        await loadDashboardData();
        setupEventListeners();
    }
});

async function loadDashboardData() {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/dashboard', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    const data = await response.json();

    document.getElementById('startShift').checked = data.startShift;
    document.getElementById('endShift').checked = data.endShift;
    document.getElementById('timeFrom').value = data.timeFrom;
    document.getElementById('timeTo').value = data.timeTo;
    document.getElementById('targetAudience').value = data.targetAudience;
}

function setupEventListeners() {
    document.getElementById('startShift').addEventListener('change', handleCheckboxChange);
    document.getElementById('endShift').addEventListener('change', handleCheckboxChange);

    const timeInputs = document.querySelectorAll('input[type="time"]');
    timeInputs.forEach(input => {
        input.addEventListener('change', handleTimeChange);
    });

    document.getElementById('targetAudience').addEventListener('input', handleTextAreaChange);
}

async function handleCheckboxChange(e) {
    const data = {
        startShift: document.getElementById('startShift').checked,
        endShift: document.getElementById('endShift').checked
    };

    await saveData('shifts', data);
}

async function handleTimeChange() {
    const timeFrom = document.getElementById('timeFrom').value;
    const timeTo = document.getElementById('timeTo').value;

    if (timeFrom && timeTo) {
        await saveData('time', { timeFrom, timeTo });
    }
}

async function handleTextAreaChange(e) {
    clearTimeout(timeoutId);

    timeoutId = setTimeout(async () => {
        await saveData('audience', {
            targetAudience: e.target.value
        });
    }, 1000);
}

async function saveData(endpoint, data) {
    try {
        const token = localStorage.getItem('token');
        await fetch(`/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
        });
    } catch (error) {
        console.error('Помилка збереження:', error);
    }
}
