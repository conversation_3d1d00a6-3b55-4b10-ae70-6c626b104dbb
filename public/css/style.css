:root {
    --primary-blue: #1e88e5;
    --light-blue: #e3f2fd;
    --white: #ffffff;
    --gray: #f5f5f5;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Arial, sans-serif;
    background-color: var(--gray);
}

.container {
    max-width: 600px;
    margin: 40px auto;
    padding: 30px;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    color: var(--primary-blue);
    margin-bottom: 30px;
    text-align: center;
    margin-top: 5px;
}

.form-group {
    margin-bottom: 20px;
}

input[type="text"],
input[type="password"],
input[type="time"],
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="time"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    background-color: var(--light-blue);
}

.checkbox-group {
    display: flex;
    gap: 20px;
    background-color: var(--light-blue);
    padding: 15px;
    border-radius: 5px;
}

.time-range {
    justify-content: center;
    display: flex;
    gap: 40px;
    background-color: var(--light-blue);
    padding: 15px;
    border-radius: 5px;
}

button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-blue);
    color: var(--white);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #1565c0;
}

textarea {
    min-height: 300px;
    width: 574px;
    resize: none;
}

label {
    display: block;
    color: #333;
    font-weight: 500;
}
.stats-container {
    background-color: var(--light-blue);
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #ddd;
}
.error-message {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 30px;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-wrapper {
    max-width: 960px;
    margin: 0 auto;
    padding: 20px;
    padding-right: 120px !important; /* Додатковий відступ зліва для зміщення контенту */
}

.header-block {
    background-color: var(--white);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 20px;
    width: calc(300px + 600px + 60px + 20px + 40px + 20px); /* stats-block + container + paddings + gap + компенсація padding */
    box-sizing: border-box;
}

.header-block h1 {
    margin: 0;
    color: var(--primary-blue);
    font-size: 24px;
}

.dashboard-layout {
    display: flex;
    gap: 20px;
    margin: 0;
    padding: 0;
}


.stats-block {
    flex: 0 0 300px;
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 0;
    max-height: 720px;
}

.main-block {
    flex: 1;
}

.container {
    width: 600px;
    margin: 0;
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 720px;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
    width: 100%;
}

.tab {
    flex: 1;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    text-align: center;
}

.tab.active {
    border-bottom: 2px solid #007bff;
    color: #007bff;
}

.stats-content {
    display: none;
    overflow-y: auto;
    max-height: 600px;
    padding-right: 12px; /* Додаємо відступ справа для уникнення перекриття тексту */
    box-sizing: border-box; /* Враховуємо padding у розмірах */
}


.stats-content.active {
    display: block;
}

.stats-content::-webkit-scrollbar {
    width: 8px;
}

.stats-content::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
}

.stats-content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

.stats-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.date-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-btn svg {
    fill: #2196f3;
}

.calendar-btn:hover svg {
    fill: #1976d2;
}
