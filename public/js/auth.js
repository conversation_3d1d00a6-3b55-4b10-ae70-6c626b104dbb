checkAuth();

async function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        if (window.location.pathname !== '/baby-moderator/index.html' && window.location.pathname !== '/') {
            window.location.href = '/baby-moderator/index.html';
        }
        return;
    }

    const response = await fetch('/baby-moderator/api/verify', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });

    const data = await response.json();

    if (data.valid) {
        if (window.location.pathname === '/baby-moderator/index.html' || window.location.pathname === '/') {
            window.location.href = '/baby-moderator/dashboard.html';
        }
    } else {
        localStorage.removeItem('token');
        window.location.href = '/index.html';
    }
}

async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    const response = await fetch('/baby-moderator/api/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
    });

    const data = await response.json();

    if (data.success) {
        localStorage.setItem('token', data.token);
        window.location.href = '/baby-moderator/dashboard.html';
    } else {
        alert('Невірний логін або пароль');
    }
}

function logout() {
    localStorage.removeItem('token');
    window.location.href = '/baby-moderator/index.html';
}
