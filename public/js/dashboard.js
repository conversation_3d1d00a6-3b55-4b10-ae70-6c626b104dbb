let timeoutId;

document.addEventListener('DOMContentLoaded', async () => {
    await loadDashboardData();
    setupWebSocket();
    setupEventListeners();

    // Ініціалізація календаря
    const calendarBtn = document.getElementById('calendarBtn');
    const dateSelector = document.getElementById('dateSelector');
    const todayTab = document.querySelector('[data-tab="today"]');

    if (calendarBtn && dateSelector && todayTab) {
        calendarBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dateSelector.showPicker();
        });

        dateSelector.addEventListener('change', async (e) => {
            const selectedDate = new Date(e.target.value);
            const today = new Date().toLocaleDateString('uk-UA', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('.').reverse().join('-');

            if (e.target.value === today) {
                todayTab.textContent = 'Сьогодні';
            } else {
                // Форматуємо дату як дд.мм.рррр
                const day = String(selectedDate.getDate()).padStart(2, '0');
                const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                const year = selectedDate.getFullYear();
                todayTab.textContent = `${day}.${month}.${year}`;
            }

            await loadStats(e.target.value);
        });
    }
});






const BASE_PATH = '/baby-moderator';


async function loadDashboardData() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${BASE_PATH}/api/dashboard`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.status === 401) {
            console.log('info', 'Токен недійсний, перенаправлення на сторінку входу');
            localStorage.removeItem('token');
            window.location.href = `${BASE_PATH}/index.html`;
            return;
        }

        const data = await response.json();

        document.getElementById('startShift').checked = data.startShift === 1 || data.startShift === "1";
        document.getElementById('endShift').checked = data.endShift === 1 || data.endShift === "1";
        document.getElementById('loadEnabled').checked = data.loadEnabled === 1 || data.loadEnabled === "1";
      document.getElementById('batchMode').checked = data.batchMode === 1 || data.batchMode === "1";

        document.getElementById('timeFrom').value = data.timeFrom || '';
        document.getElementById('timeTo').value = data.timeTo || '';

        const formattedAudience = data.targetAudience ? data.targetAudience.replace(/;/g, '\n') : '';
        document.getElementById('targetAudience').value = formattedAudience;

        console.log('Завантажені налаштування:', data);
        await loadStats();
        // if (!statsInterval) {
        //     statsInterval = setInterval(loadStats, 1000);
        //     await loadStats();
        // }
    } catch (error) {
        console.error('Помилка завантаження даних:', error);
        localStorage.removeItem('token');
        window.location.href = `${BASE_PATH}/index.html`;
    }
}


function logout() {
    if (statsInterval) {
        clearInterval(statsInterval);
    }
    localStorage.removeItem('token');
    window.location.href = `${BASE_PATH}/index.html`;
}


async function handleTextAreaChange(e) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(async () => {
        // Прибираємо зайві пробіли на початку і в кінці
        const text = e.target.value.trim();
        // Замінюємо всі можливі варіанти переносів рядка на ;
        const formattedText = text.replace(/[\r\n]+/g, ';');

        await saveData('audience', {
            targetAudience: formattedText
        });
    }, 1000);
}




function resetToDefaults() {
    document.getElementById('startShift').checked = false;
    document.getElementById('endShift').checked = false;
    document.getElementById('loadEnabled').checked = false;
  document.getElementById('batchMode').checked = false;
    document.getElementById('timeFrom').value = '';
    document.getElementById('timeTo').value = '';
    document.getElementById('targetAudience').value = '';
}

function setupEventListeners() {
    const startShiftCheckbox = document.getElementById('startShift');
    const endShiftCheckbox = document.getElementById('endShift');
    const loadEnabledCheckbox = document.getElementById('loadEnabled');
  const batchModeCheckbox = document.getElementById('batchMode');
    const timeFromInput = document.getElementById('timeFrom');
    const timeToInput = document.getElementById('timeTo');
    const targetAudienceTextarea = document.getElementById('targetAudience');

    startShiftCheckbox.addEventListener('change', handleCheckboxChange);
    endShiftCheckbox.addEventListener('change', handleCheckboxChange);
    loadEnabledCheckbox.addEventListener('change', handleCheckboxChange);
  batchModeCheckbox.addEventListener('change', handleCheckboxChange);
    timeFromInput.addEventListener('change', handleTimeChange);
    timeToInput.addEventListener('change', handleTimeChange);
    targetAudienceTextarea.addEventListener('input', handleTextAreaChange);
}

async function handleCheckboxChange() {
    const data = {
        startShift: document.getElementById('startShift').checked,
        endShift: document.getElementById('endShift').checked,
      loadEnabled: document.getElementById('loadEnabled').checked,
      batchMode: document.getElementById('batchMode').checked
    };
    await saveData('shifts', data);
}

async function handleTimeChange() {
    const timeFrom = document.getElementById('timeFrom').value;
    const timeTo = document.getElementById('timeTo').value;

    if (timeFrom && timeTo) {
        await saveData('time', { timeFrom, timeTo });
    }
}

async function handleTextAreaChange(e) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(async () => {
        await saveData('audience', {
            targetAudience: e.target.value
        });
    }, 1000);
}

async function saveData(endpoint, data) {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${BASE_PATH}/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
        });

        if (response.status === 401) {
            console.log('info', 'Токен недійсний, перенаправлення на сторінку входу');
            localStorage.removeItem('token');
            window.location.href = `${BASE_PATH}/index.html`;
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log(`Дані успішно збережені для ${endpoint}:`, data);
    } catch (error) {
        console.error('Помилка збереження:', error);
    }
}

function setupWebSocket() {
    const ws = new WebSocket(`wss://${window.location.host}/baby-moderator`);
    const audio = new Audio('/baby-moderator/assets/notification.mp3');

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'log') {
            if (data.message.includes('⚡')) { //|| data.message.includes('✓ Отримано статус')
                console.log('[Server]:', data.message);
            }
        }
        if (data.type === 'audio') {
            audio.currentTime = 0; // Скидаємо час відтворення
            audio.play().catch(error => {
                console.error('Помилка відтворення аудіо:', error);
            });
        }
    };

    ws.onclose = () => {
        setTimeout(setupWebSocket, 5000);
    };

    ws.onerror = (error) => {
        console.error('WebSocket помилка:', error);
    };
}


let selectedGlobalDate = null;
let statsInterval = null;

async function loadStats(selectedDate = null) {
    try {
        const token = localStorage.getItem('token');
        const today = new Date().toLocaleDateString('uk-UA', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('.').reverse().join('-');

        // Очищаємо попередній інтервал при виборі нової дати
        if (selectedDate && statsInterval) {
            clearInterval(statsInterval);
            statsInterval = null;
        }

        // Оновлюємо глобальну дату
        if (selectedDate) {
            selectedGlobalDate = selectedDate;
        }

        const url = selectedGlobalDate
            ? `${BASE_PATH}/api/monitoring-stats?date=${selectedGlobalDate}`
            : `${BASE_PATH}/api/monitoring-stats`;

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const stats = await response.json();
        const allTimeStats = document.getElementById('allTimeStats');
        const todayStats = document.getElementById('todayStats');
        const todayTab = document.querySelector('[data-tab="today"]');

        // Оновлюємо текст вкладки
        if (selectedGlobalDate === today || !selectedGlobalDate) {
            todayTab.textContent = 'Сьогодні';
            selectedGlobalDate = null;
        }

        allTimeStats.innerHTML = '';
        todayStats.innerHTML = '';

        // Відображення статистики за обрану дату
        const todayEntries = Object.entries(stats.daily[selectedGlobalDate || today] || {})
            .sort(([, a], [, b]) => b - a);

        const allTimeEntries = Object.entries(stats.allTime || {})
            .sort(([, a], [, b]) => b - a);

        // Відображаємо статистику за весь час
        allTimeEntries.forEach(([username, count]) => {
            const div = document.createElement('div');
            div.className = 'stat-item';
            div.innerHTML = `
                <span>${username}</span>
                <span>${count} змін</span>
            `;
            allTimeStats.appendChild(div);
        });

        // Відображаємо статистику за обрану дату
        todayEntries.forEach(([username, count]) => {
            const div = document.createElement('div');
            div.className = 'stat-item';
            div.innerHTML = `
                <span>${username}</span>
                <span>${count} змін</span>
            `;
            todayStats.appendChild(div);
        });

        // Додаємо обробники для вкладок
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.stats-content').forEach(c => c.classList.remove('active'));

                tab.classList.add('active');
                const contentId = tab.getAttribute('data-tab') === 'all-time' ? 'allTimeStats' : 'todayStats';
                document.getElementById(contentId).classList.add('active');
            });
        });

        // Встановлюємо інтервал тільки якщо дивимось поточну дату
        if (!selectedGlobalDate && !statsInterval) {
            statsInterval = setInterval(() => loadStats(), 1000);
        }
    } catch (error) {
        const errorMessage = `<div class="error-message">Помилка завантаження статистики: ${error.message}</div>`;
        document.getElementById('allTimeStats').innerHTML = errorMessage;
        document.getElementById('todayStats').innerHTML = errorMessage;
    }
}



