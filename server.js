const express = require('express');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');
const http = require('http');
const { initWebSocket } = require('./websocket');
const { startMonitoring } = require('./monitoring');
const path = require('path');
const fs = require('fs').promises;
const console = require('./utils/logging.js');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
initWebSocket(server);

const JWT_SECRET = process.env.JWT_SECRET;
const listeningPort = 3030;
const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    enableKeepAlive: true,
    keepAliveInitialDelay: 0,
    port: process.env.PORT || 3306
};

// const wss = new WebSocket.Server({
//     server: server,
//     path: '/baby-moderator'
// });

// const clients = new Set();
// wss.on('connection', (ws) => {
//     clients.add(ws);

//     ws.on('close', () => {
//         clients.delete(ws);
//     });
// });
// const originalLog = console.log;
// console.log = function () {
//     originalLog.apply(console, arguments);
//     const logMessage = Array.from(arguments).join(' ');
//     clients.forEach(client => {
//         if (client.readyState === WebSocket.OPEN) {
//             client.send(JSON.stringify({
//                 type: 'log',
//                 message: logMessage
//             }));
//         }
//     });
// };



let connection;

async function initDB() {
    try {
        // Спочатку підключаємося без вказання бази даних
        const tempConnection = mysql.createPool({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            enableKeepAlive: true,
            keepAliveInitialDelay: 0,
            port: process.env.PORT || 3306
        });

        // Створюємо базу даних, якщо не існує
        await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME}\``);
        
        // Закриваємо тимчасове з'єднання
        await tempConnection.end();

        // Тепер підключаємося до створеної бази даних
        connection = mysql.createPool(dbConfig);
        console.log('Підключено до БД');

        // Використовуємо створену базу даних
        await connection.execute(`USE \`${process.env.DB_NAME}\``);

        // Створюємо таблиці
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS settings (
                id int(11) NOT NULL AUTO_INCREMENT,
                startShift int(11) DEFAULT 0 COMMENT 'Моніторити початок зміни',
                endShift int(11) DEFAULT 0 COMMENT 'Моніторити кінець зміни',
                loadEnabled int(11) DEFAULT 0 COMMENT 'Моніторити по інтервалу',
                timeFrom text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, початок інтервалу',
                timeTo text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, кінець інтервалу',
                targetAudience longtext DEFAULT NULL COMMENT 'Список агентів, в яких життя стане кращим, без відсутності зловживання режимами ХРМ.',
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        `);

        await connection.execute(`
            CREATE TABLE IF NOT EXISTS statistics_daily (
                id int(11) NOT NULL AUTO_INCREMENT,
                username varchar(255) NOT NULL,
                date date NOT NULL,
                changes_count int(11) DEFAULT 1,
                created_at timestamp NULL DEFAULT current_timestamp(),
                PRIMARY KEY (id),
                UNIQUE KEY unique_user_date (username, date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        `);

        await connection.execute(`
            CREATE TABLE IF NOT EXISTS statistics_total (
                id int(11) NOT NULL AUTO_INCREMENT,
                username varchar(255) NOT NULL,
                total_changes int(11) DEFAULT 1,
                updated_at timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (id),
                UNIQUE KEY username (username)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        `);

        await connection.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id int(11) NOT NULL AUTO_INCREMENT,
                login varchar(150) NOT NULL,
                password varchar(500) NOT NULL,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        `);

        // Перевіряємо чи існує запис в таблиці settings
        const [rows] = await connection.execute('SELECT * FROM settings LIMIT 1');
        if (rows.length === 0) {
            await connection.execute(`
                INSERT INTO settings (startShift, endShift, timeFrom, timeTo, targetAudience) 
                VALUES (0, 0, '14:00', '16:00', '')
            `);
        }

        // Обробка помилок підключення
        connection.on('error', (err) => {
            if (err.code === 'ECONNRESET') {
                console.log('Відбулось перепідключення до бази даних');
            }
        });

        return connection;
    } catch (err) {
        console.error('Помилка підключення до БД:', err);
        throw err;
    }
}

initDB();

app.use(express.json());
app.use('/baby-moderator', express.static('public'));

// function sendAudioToClients(audioFileName) {
//     clients.forEach(client => {
//         if (client.readyState === WebSocket.OPEN) {
//             client.send(JSON.stringify({
//                 type: 'audio',
//                 file: `/baby-moderator/audio/${audioFileName}`
//             }));
//         }
//     });
// }

const authenticateToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'Необхідна авторизація' });

    try {
        const user = jwt.verify(token, JWT_SECRET);
        req.user = user;
        next();
    } catch {
        res.status(401).json({ error: 'Невірний токен' });
    }
};

app.post('/baby-moderator/api/login', async (req, res) => {
    const { username, password } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;

    try {
        const [rows] = await connection.execute(
            'SELECT * FROM users WHERE login = ? AND password = ?',
            [username, password]
        );

        if (rows.length > 0) {
            const token = jwt.sign({ id: rows[0].id, username }, JWT_SECRET);
            console.log('info', `${clientIP} | Успішна авторизація користувача ${username}`);
            res.json({ success: true, token });
        } else {
            console.log('warn', `${clientIP} | Невдала спроба авторизації користувача ${username}`);
            res.status(401).json({ success: false });
        }
    } catch (err) {
        console.log('error', `${clientIP} | Помилка авторизації для ${username}: ${err.message}`);
        res.status(500).json({ success: false });
    }
});

app.get('/baby-moderator/api/dashboard', authenticateToken, async (req, res) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    try {
        const [rows] = await connection.execute('SELECT * FROM settings LIMIT 1');
        console.log('info', `${clientIP} | Отримання налаштувань дашборду користувачем ${req.user.username}`);
        res.json(rows[0] || {});
    } catch (err) {
        console.log('error', `${clientIP} | Помилка отримання налаштувань: ${err.message}`);
        res.status(500).json({ error: 'Помилка сервера' });
    }
});

app.post('/baby-moderator/api/shifts', authenticateToken, async (req, res) => {
    const { startShift, endShift, loadEnabled } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    try {
        const [rows] = await connection.execute('SELECT * FROM settings LIMIT 1');
        const action = rows.length === 0 ? 'Створення' : 'Оновлення';

        if (rows.length === 0) {
            await connection.execute(
                'INSERT INTO settings (startShift, endShift, loadEnabled) VALUES (?, ?, ?)',
                [startShift, endShift, loadEnabled]
            );
        } else {
            await connection.execute(
                'UPDATE settings SET startShift = ?, endShift = ?, loadEnabled = ?',
                [startShift, endShift, loadEnabled]
            );
        }
        console.log('info', `${clientIP} | ${action} налаштувань змін користувачем ${req.user.username}: startShift=${startShift}, endShift=${endShift}, loadEnabled=${loadEnabled}`);
        res.json({ success: true });
    } catch (err) {
        console.log('error', `${clientIP} | Помилка ${err.message}`);
        res.status(500).json({ error: 'Помилка сервера' });
    }
});

app.post('/baby-moderator/api/time', authenticateToken, async (req, res) => {
    const { timeFrom, timeTo } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    try {
        const [rows] = await connection.execute('SELECT * FROM settings LIMIT 1');
        const action = rows.length === 0 ? 'створення' : 'оновлення';

        if (rows.length === 0) {
            await connection.execute(
                'INSERT INTO settings (timeFrom, timeTo) VALUES (?, ?)',
                [timeFrom, timeTo]
            );
        } else {
            await connection.execute(
                'UPDATE settings SET timeFrom = ?, timeTo = ?',
                [timeFrom, timeTo]
            );
        }
        console.log('info', `${clientIP} | ${action} налаштувань часу користувачем ${req.user.username}: ${timeFrom}-${timeTo}`);
        res.json({ success: true });
    } catch (err) {
        console.log('error', `${clientIP} | Помилка збереження часу: ${err.message}`);
        res.status(500).json({ error: 'Помилка сервера' });
    }
});

app.post('/baby-moderator/api/audience', authenticateToken, async (req, res) => {
    let { targetAudience } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    targetAudience = targetAudience.replace(/[\r\n]+/g, ';');

    try {
        const [rows] = await connection.execute('SELECT * FROM settings LIMIT 1');
        const action = rows.length === 0 ? 'створення' : 'оновлення';

        if (rows.length === 0) {
            await connection.execute(
                'INSERT INTO settings (targetAudience) VALUES (?)',
                [targetAudience]
            );
        } else {
            await connection.execute(
                'UPDATE settings SET targetAudience = ?',
                [targetAudience]
            );
        }
        console.log('info', `${clientIP} | ${action} списку цільової аудиторії користувачем ${req.user.username}. 
Кількість логінів: ${targetAudience.split(';').filter(Boolean).length}
Список логінів: ${targetAudience.split(';').filter(Boolean).join(', ')}`);
        res.json({ success: true });
    } catch (err) {
        console.log('error', `${clientIP} | Помилка збереження аудиторії: ${err.message}`);
        res.status(500).json({ error: 'Помилка сервера' });
    }
});


app.get('/baby-moderator/api/verify', (req, res) => {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) return res.status(401).json({ valid: false });

    try {
        jwt.verify(token, JWT_SECRET);
        res.json({ valid: true });
    } catch (err) {
        res.status(401).json({ valid: false });
    }
});

app.get('/baby-moderator/api/monitoring-stats', authenticateToken, async (req, res) => {
    try {
        const { date } = req.query;
        const today = new Date().toLocaleDateString('uk-UA', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('.').reverse().join('-');
        const targetDate = date || today;

        // Отримуємо статистику за конкретну дату
        const [dailyStats] = await connection.execute(`
            SELECT username, changes_count 
            FROM statistics_daily 
            WHERE date = ?
        `, [targetDate]);

        // Отримуємо загальну статистику
        const [totalStats] = await connection.execute(`
            SELECT username, total_changes 
            FROM statistics_total
        `);

        // Форматуємо відповідь
        const response = {
            daily: {
                [targetDate]: {}
            },
            allTime: {}
        };

        // Форматуємо щоденну статистику
        dailyStats.forEach(stat => {
            response.daily[targetDate][stat.username] = stat.changes_count;
        });

        // Форматуємо загальну статистику
        totalStats.forEach(stat => {
            response.allTime[stat.username] = stat.total_changes;
        });

        res.json(response);
    } catch (error) {
        console.error('Помилка отримання статистики:', error);
        res.json({
            daily: {},
            allTime: {}
        });
    }
});



app.get('/baby-moderator/api/test', async (req, res) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    try {
        // Перевіряємо з'єднання з БД
        const [rows] = await connection.execute('SELECT 1');
        const dbStatus = rows.length > 0 ? 'Активний' : 'Неактивний';
        const timestamp = new Date().toISOString();

        console.log('info', `${clientIP} | Перевірка стану додатку: Додаток - Активний, БД - ${dbStatus} (${timestamp})`);

        res.json({
            application: {
                status: 'Активний',
                timestamp
            },
            database: {
                status: dbStatus,
                timestamp
            }
        });
    } catch (err) {
        const timestamp = new Date().toISOString();
        console.log('error', `${clientIP} | Перевірка стану додатку: Додаток - Активний, БД - Неактивний (${timestamp}). Помилка: ${err.message}`);

        res.json({
            application: {
                status: 'Активний',
                timestamp
            },
            database: {
                status: 'Неактивний',
                timestamp
            }
        });
    }
});



server.listen(listeningPort, () => {
    console.log(`Server was listening ${listeningPort}`);
    startMonitoring();
});

// module.exports = {
//     wss,
//     clients,
//     sendAudioToClients
// };