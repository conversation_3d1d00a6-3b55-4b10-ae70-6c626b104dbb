# Baby Moderator API Documentation

## Project Overview

Baby Moderator is an application designed to monitor and manage user statuses within the XRM system. It automatically changes user statuses from manual mode to outgoing call mode after a specified period and tracks these changes for statistical purposes.

## API Endpoints

### Authentication

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/baby-moderator/api/login` | POST | Authenticates a user and returns a JWT token |
| `/baby-moderator/api/verify` | GET | Verifies if a JWT token is valid |

#### Login Request
```json
{
  "username": "string",
  "password": "string"
}
```

#### Login Response
```json
{
  "success": true,
  "token": "JWT_TOKEN_STRING"
}
```

### Settings Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/baby-moderator/api/dashboard` | GET | Retrieves current dashboard settings |
| `/baby-moderator/api/shifts` | POST | Updates shift monitoring settings |
| `/baby-moderator/api/time` | POST | Updates custom time interval settings |
| `/baby-moderator/api/audience` | POST | Updates the list of target users to monitor |

#### Shift Settings Request
```json
{
  "startShift": 0/1,
  "endShift": 0/1,
  "loadEnabled": 0/1
}
```

#### Time Settings Request
```json
{
  "timeFrom": "HH:MM",
  "timeTo": "HH:MM"
}
```

#### Target Audience Request
```json
{
  "targetAudience": "username1;username2;username3"
}
```

### Statistics

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/baby-moderator/api/monitoring-stats` | GET | Retrieves monitoring statistics for a specific date or today |

#### Monitoring Stats Response
```json
{
  "daily": {
    "YYYY-MM-DD": {
      "username1": 5,
      "username2": 3
    }
  },
  "allTime": {
    "username1": 42,
    "username2": 27
  }
}
```

### System Status

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/baby-moderator/api/test` | GET | Tests application and database connectivity |

#### Test Response
```json
{
  "application": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO datetime"
  },
  "database": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO datetime"
  }
}
```

## Database Structure

The application uses a MariaDB database with the following tables:

### Settings Table

```sql
CREATE TABLE IF NOT EXISTS settings (
    id int(11) NOT NULL AUTO_INCREMENT,
    startShift int(11) DEFAULT 0 COMMENT 'Моніторити початок зміни',
    endShift int(11) DEFAULT 0 COMMENT 'Моніторити кінець зміни',
    loadEnabled int(11) DEFAULT 0 COMMENT 'Моніторити по інтервалу',
    timeFrom text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, початок інтервалу',
    timeTo text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, кінець інтервалу',
    targetAudience longtext DEFAULT NULL COMMENT 'Список агентів, в яких життя стане кращим, без відсутності зловживання режимами ХРМ.',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Daily Statistics Table

```sql
CREATE TABLE IF NOT EXISTS statistics_daily (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(255) NOT NULL,
    date date NOT NULL,
    changes_count int(11) DEFAULT 1,
    created_at timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_date (username, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Total Statistics Table

```sql
CREATE TABLE IF NOT EXISTS statistics_total (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(255) NOT NULL,
    total_changes int(11) DEFAULT 1,
    updated_at timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Users Table

```sql
CREATE TABLE IF NOT EXISTS users (
    id int(11) NOT NULL AUTO_INCREMENT,
    login varchar(150) NOT NULL,
    password varchar(500) NOT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

## External API Interactions

The application interacts with the Kyivstar XRM Gateway API for monitoring and managing user statuses:

### XRM API Endpoints Used

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall` | GET | Search for user ID by username |
| `https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId` | GET | Get the current status of a user |
| `https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck` | POST | Change a user's status |

### XRM Status Change Request Example

```json
{
  "StatusId": "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
  "UserId": "user-uuid",
  "RoleId": "7e2984c7-430b-4b57-abd1-e7976933c7ad",
  "ip": "Auto",
  "os": "Майкрософт Windows 10 Корпоративная LTSC"
}
```

## Monitoring Logic

The application implements the following monitoring logic:

1. Fetches the target user list from the database
2. Monitors users based on configured time settings:
   - Start of shift (9:00-9:30)
   - End of shift (17:25-17:55)
   - Custom time interval
3. Groups users for processing (4 users at a time)
4. For each user:
   - Gets user ID via XRM API
   - Checks current status via XRM API
   - If user is in "Исх - Ручной режим" for more than 60 seconds:
     - Changes status to "Исходящий обзвон"
     - Updates statistics in database
5. Keeps track of online and offline users
6. Checks offline users every 60 seconds to detect when they come back online

## System Requirements

- Node.js
- MariaDB database
- Network access to XRM Gateway API
- Environment variables configuration (.env file)

## Deployment Instructions

1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables in .env file
4. Start the server: `node server.js`

---

# Документація API Baby Moderator

## Огляд проекту

Baby Moderator - це додаток, розроблений для моніторингу та управління статусами користувачів у системі XRM. Він автоматично змінює статуси користувачів з ручного режиму на режим вихідного дзвінка після визначеного періоду та відстежує ці зміни для статистичних цілей.

## Ендпоінти API

### Аутентифікація

| Ендпоінт | Метод | Опис |
|----------|--------|-------------|
| `/baby-moderator/api/login` | POST | Аутентифікує користувача та повертає JWT токен |
| `/baby-moderator/api/verify` | GET | Перевіряє валідність JWT токена |

#### Запит на авторизацію
```json
{
  "username": "string",
  "password": "string"
}
```

#### Відповідь на авторизацію
```json
{
  "success": true,
  "token": "JWT_TOKEN_STRING"
}
```

### Управління налаштуваннями

| Ендпоінт | Метод | Опис |
|----------|--------|-------------|
| `/baby-moderator/api/dashboard` | GET | Отримує поточні налаштування панелі управління |
| `/baby-moderator/api/shifts` | POST | Оновлює налаштування моніторингу змін |
| `/baby-moderator/api/time` | POST | Оновлює налаштування користувацького часового інтервалу |
| `/baby-moderator/api/audience` | POST | Оновлює список цільових користувачів для моніторингу |

#### Запит налаштувань змін
```json
{
  "startShift": 0/1,
  "endShift": 0/1,
  "loadEnabled": 0/1
}
```

#### Запит налаштувань часу
```json
{
  "timeFrom": "ГГ:ХХ",
  "timeTo": "ГГ:ХХ"
}
```

#### Запит цільової аудиторії
```json
{
  "targetAudience": "username1;username2;username3"
}
```

### Статистика

| Ендпоінт | Метод | Опис |
|----------|--------|-------------|
| `/baby-moderator/api/monitoring-stats` | GET | Отримує статистику моніторингу за конкретну дату або сьогодні |

#### Відповідь статистики моніторингу
```json
{
  "daily": {
    "РРРР-ММ-ДД": {
      "username1": 5,
      "username2": 3
    }
  },
  "allTime": {
    "username1": 42,
    "username2": 27
  }
}
```

### Стан системи

| Ендпоінт | Метод | Опис |
|----------|--------|-------------|
| `/baby-moderator/api/test` | GET | Тестує підключення додатку та бази даних |

#### Відповідь тесту
```json
{
  "application": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  },
  "database": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  }
}
```

## Структура бази даних

Додаток використовує базу даних MariaDB з наступними таблицями:

### Таблиця налаштувань (Settings)

```sql
CREATE TABLE IF NOT EXISTS settings (
    id int(11) NOT NULL AUTO_INCREMENT,
    startShift int(11) DEFAULT 0 COMMENT 'Моніторити початок зміни',
    endShift int(11) DEFAULT 0 COMMENT 'Моніторити кінець зміни',
    loadEnabled int(11) DEFAULT 0 COMMENT 'Моніторити по інтервалу',
    timeFrom text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, початок інтервалу',
    timeTo text DEFAULT NULL COMMENT 'Моніторинг плаваючого часу, кінець інтервалу',
    targetAudience longtext DEFAULT NULL COMMENT 'Список агентів, в яких життя стане кращим, без відсутності зловживання режимами ХРМ.',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Таблиця щоденної статистики (Daily Statistics)

```sql
CREATE TABLE IF NOT EXISTS statistics_daily (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(255) NOT NULL,
    date date NOT NULL,
    changes_count int(11) DEFAULT 1,
    created_at timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_date (username, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Таблиця загальної статистики (Total Statistics)

```sql
CREATE TABLE IF NOT EXISTS statistics_total (
    id int(11) NOT NULL AUTO_INCREMENT,
    username varchar(255) NOT NULL,
    total_changes int(11) DEFAULT 1,
    updated_at timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (id),
    UNIQUE KEY username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### Таблиця користувачів (Users)

```sql
CREATE TABLE IF NOT EXISTS users (
    id int(11) NOT NULL AUTO_INCREMENT,
    login varchar(150) NOT NULL,
    password varchar(500) NOT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

## Взаємодія із зовнішніми API

Додаток взаємодіє з Kyivstar XRM Gateway API для моніторингу та управління статусами користувачів:

### Ендпоінти XRM API, що використовуються

| Ендпоінт | Метод | Призначення |
|----------|--------|---------|
| `https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall` | GET | Пошук ID користувача за ім'ям користувача |
| `https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId` | GET | Отримання поточного статусу користувача |
| `https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck` | POST | Зміна статусу користувача |

### Приклад запиту на зміну статусу XRM

```json
{
  "StatusId": "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
  "UserId": "user-uuid",
  "RoleId": "7e2984c7-430b-4b57-abd1-e7976933c7ad",
  "ip": "Auto",
  "os": "Майкрософт Windows 10 Корпоративная LTSC"
}
```

## Логіка моніторингу

Додаток реалізує наступну логіку моніторингу:

1. Отримує список цільових користувачів з бази даних
2. Моніторить користувачів на основі налаштованих часових параметрів:
   - Початок зміни (9:00-9:30)
   - Кінець зміни (17:25-17:55)
   - Користувацький часовий інтервал
3. Групує користувачів для обробки (по 4 користувачі за раз)
4. Для кожного користувача:
   - Отримує ID користувача через XRM API
   - Перевіряє поточний статус через XRM API
   - Якщо користувач у статусі "Исх - Ручной режим" більше 60 секунд:
     - Змінює статус на "Исходящий обзвон"
     - Оновлює статистику в базі даних
5. Відстежує онлайн та офлайн користувачів
6. Перевіряє офлайн користувачів кожні 60 секунд, щоб виявити, коли вони повернуться в онлайн

## Системні вимоги

- Node.js
- База даних MariaDB
- Доступ до мережі для роботи з XRM Gateway API
- Налаштування змінних середовища (.env файл)

## Інструкції з розгортання

1. Клонувати репозиторій
2. Встановити залежності: `npm install`
3. Налаштувати змінні середовища в .env файлі
4. Запустити сервер: `node server.js`
