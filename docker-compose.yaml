services:
  app:
    build:
      context: .
      args:
        HTTP_PROXY: $HTTP_PROXY
        HTTPS_PROXY: $HTTP_PROXY
        NO_PROXY: $NO_PROXY
    container_name: $CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
    image: "localhost:5000/${CI_PROJECT_NAME}:latest"
    environment:
      PROJECT_NAME: $CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
    env_file:
    -  $ENV_FILE
    volumes:
    - /store/app/CCA/logs:/app/logs:z
    healthcheck:
      test: ps aux | grep node | grep -v grep &> /dev/null && exit 0 || exit 1
      interval: 5s
      timeout: 2s
      retries: 3
    networks:
    - apis
    restart: always

networks:
  apis:
    external: true

