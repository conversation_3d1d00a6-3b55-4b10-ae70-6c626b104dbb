# Baby Moderator
In the veranda, oh yeah😏
## Опис
Не треба балуваться...
Веб-додаток для моніторингу та автоматичної зміни статусів операторів у системі Softphone.

## Режими роботи
* Моніторинг початку зміни (9:00-9:30)
* Моніторинг кінця зміни (17:30-18:00)
* Моніторинг у кастомному інтервалі часу

## Основні функції
* Автоматична зміна статусу "Исх - Ручной режим" на "Исходящий обзвон"
* Веб-інтерфейс для керування налаштуваннями моніторингу
* Статистика змін статусів по операторах
* Паралельна обробка запитів для оптимізації швидкодії

## Встановлення та налаштування

1. Клонуйте репозиторій
2. Встановіть залежності:
```bash
npm install
```

3. Створіть файл .env з наступними параметрами:
```env
DB_HOST="localhost"
DB_USER="user"
DB_PASSWORD="password"
DB_NAME="baby-moderator"
JWT_SECRET="f7a8b9c0d1e2f3g4h5i6j7k8l9m0n1o2"
```

## Тестування
Для перевірки працездатності додатку виконайте GET-запит:
```
GET http://localhost:3030/baby-moderator/api/test
```

Успішна відповідь:
```json
{
    "application": {
        "status": "Активний",
        "timestamp": "2025-02-07T14:02:01.222Z"
    },
    "database": {
        "status": "Активний",
        "timestamp": "2025-02-07T14:02:01.222Z"
    }
}
```

---