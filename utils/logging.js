const log4js = require("log4js");

log4js.configure({
    appenders: {
        '[baby-moderator]': {
            type: 'dateFile',
            filename: `./logs/${process.env.PROJECT_NAME}.log`,
            numBackups: 30,
            compress: true,
            maxLogSize: 10485760,
            keepFileExt: true,
            mode: 0o664
        },
        console: { type: 'console' }
    },
    categories: {
        default: {
            appenders: ['[baby-moderator]', 'console'],
            level: 'trace'
        }
    }
});

const logger = log4js.getLogger('[baby-moderator]');

module.exports = logger;

