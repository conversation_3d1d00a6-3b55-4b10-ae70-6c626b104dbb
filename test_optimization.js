const axios = require('axios');

async function testOptimizationFeature() {
    const baseURL = 'http://localhost:3030/baby-moderator/api';
    
    try {
        console.log('🔍 Testing Optimization Feature...\n');
        
        // Step 1: Login
        console.log('1. Testing login...');
        const loginResponse = await axios.post(`${baseURL}/login`, {
            username: 'admin',
            password: 'admin'
        });
        
        if (loginResponse.data.success) {
            console.log('✅ Login successful');
            const token = loginResponse.data.token;
            
            // Step 2: Get current settings
            console.log('\n2. Getting current settings...');
            const settingsResponse = await axios.get(`${baseURL}/dashboard`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            console.log('Current settings:', settingsResponse.data);
            
            // Step 3: Test enabling batch mode
            console.log('\n3. Enabling batch mode...');
            const enableBatchResponse = await axios.post(`${baseURL}/shifts`, {
                startShift: settingsResponse.data.startShift || false,
                endShift: settingsResponse.data.endShift || false,
                loadEnabled: settingsResponse.data.loadEnabled || false,
                batchMode: true
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (enableBatchResponse.data.success) {
                console.log('✅ Batch mode enabled successfully');
            }
            
            // Step 4: Verify batch mode is enabled
            console.log('\n4. Verifying batch mode is enabled...');
            const verifyResponse = await axios.get(`${baseURL}/dashboard`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            console.log('Updated settings:', verifyResponse.data);
            
            if (verifyResponse.data.batchMode === 1) {
                console.log('✅ Batch mode is correctly enabled');
            } else {
                console.log('❌ Batch mode is not enabled');
            }
            
            // Step 5: Test disabling batch mode
            console.log('\n5. Disabling batch mode...');
            const disableBatchResponse = await axios.post(`${baseURL}/shifts`, {
                startShift: verifyResponse.data.startShift || false,
                endShift: verifyResponse.data.endShift || false,
                loadEnabled: verifyResponse.data.loadEnabled || false,
                batchMode: false
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (disableBatchResponse.data.success) {
                console.log('✅ Batch mode disabled successfully');
            }
            
            // Step 6: Final verification
            console.log('\n6. Final verification...');
            const finalResponse = await axios.get(`${baseURL}/dashboard`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            console.log('Final settings:', finalResponse.data);
            
            if (finalResponse.data.batchMode === 0) {
                console.log('✅ Batch mode is correctly disabled');
            } else {
                console.log('❌ Batch mode is still enabled');
            }
            
            console.log('\n🎉 All tests completed successfully!');
            
        } else {
            console.log('❌ Login failed');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Run the test
testOptimizationFeature();
