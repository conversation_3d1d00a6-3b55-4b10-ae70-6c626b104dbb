const axios = require('axios');
const https = require('https');
// const fs = require('fs').promises;
// const path = require('path');
const mysql = require('mysql2/promise');
const console = require('./utils/logging.js');
// const { sendAudioToClients } = require('./websocket');
require('dotenv').config();

const httpsAgent = new https.Agent({
    rejectUnauthorized: false
});

// Усі статуси з системи
const STATUSES = {
    OFFLINE: {
        id: "6d336b5a-ae6d-4c86-2be6-08d62235959d",
        statusName: "Не в системе"
    },
    ONLINE: {
        id: "8ec76347-dc0a-4c0a-1ff3-08d69d51a7cd",
        statusName: "В системе"
    },
    READY: {
        id: "458db7ca-a457-4958-1aec-08d6e9911ee3",
        statusName: "Готов"
    },
    IN_TALK: {
        id: "7583ab29-9d28-4b16-87d0-12edac838de7",
        statusName: "В разговоре"
    },
    TECH_BREAK: {
        id: "003bf0ec-fee4-464e-a338-188651aa03f3",
        statusName: "Техн. перерыв"
    },
    POST_PROCESSING: {
        id: "5cc8fbc3-fb65-4a91-809a-1bd87b93b5af",
        statusName: "Постобработка"
    },
    MANAGER_TASK: {
        id: "a94fbd7d-355f-4739-b9ce-29e3b4ba1adf",
        statusName: "Задание руководителя"
    },
    TTL_WORK: {
        id: "0d7892ad-85ba-4c87-86c7-2a7c5ccfa143",
        statusName: "Перенос сроков в TTL"
    },
    WORK_WITH_TTL: {
        id: "ff5ae205-5002-4254-bd1c-3b77cb77e659",
        statusName: "Работа с TTL"
    },
    MANUAL_MODE: {
        id: "9d9dfc7d-f56a-498d-a007-3d7027e68d31",
        statusName: "Исх - Ручной режим"
    },
    SUFLER: {
        id: "ead19170-ab18-4c85-9551-490e19e2b8e9",
        statusName: "Суфлер"
    },
    NR_CALL: {
        id: "ea315da3-b080-4ec3-9eb6-55eb22527a40",
        statusName: "NR_звонок абоненту"
    },
    TRAINING: {
        id: "f323018f-1814-49c5-82c2-63080ff313de",
        statusName: "Тренинг в рабочее время"
    },
    IN_CHAT: {
        id: "3be70b92-b137-4995-b21e-6c59c0db9586",
        statusName: "В чате"
    },
    TEMPLATE_EDITING: {
        id: "688fbbfa-7e6a-4a55-853b-75c3b5a3a80f",
        statusName: "Редактирование шаблонов"
    },
    WORK_WITH_MANAGER: {
        id: "c94ce15a-e2a4-49b2-b1b4-8159f9260154",
        statusName: "Работа с руководителем"
    },
    PREVIEW: {
        id: "59259652-e2ed-4f86-a987-8646a16ada2a",
        statusName: "Preview"
    },
    OUTGOING_CALL: {
        id: "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
        statusName: "Исходящий обзвон"
    },
    TECH_PROBLEMS: {
        id: "be69ecac-3cd6-472e-a45f-a1a1f0f7911b",
        statusName: "Исх - Техн. проблемы"
    },
    LUNCH: {
        id: "fd87f85f-1aef-40a5-bff4-aed18c1a67b8",
        statusName: "Обід"
    },
    PERSONAL: {
        id: "b06ded7a-3cac-41eb-9ee8-b0dacff2a05e",
        statusName: "В личных целях"
    },
    WORK_WITH_ACCIDENT: {
        id: "cfe1b8dc-8137-4dd2-805d-c3e4ff28061f",
        statusName: "Работа с Аварией (ВЛП)"
    },
    NR_TRAINING: {
        id: "ed9ac10c-1027-43dc-8c21-d103eef6283b",
        statusName: "NR_стажировка новеньких"
    },
    PLANNED_LUNCH: {
        id: "88c6c4f7-1fb3-4d4a-b6d9-d1a63d8087c7",
        statusName: "Плановый обед"
    },
    EMAIL_WORK: {
        id: "57f3108d-c0f6-4437-b904-e256b4c542a2",
        statusName: "Работа с E-mail"
    },
    REQUEST_REGISTRATION: {
        id: "f7721f17-0953-4ac6-aee9-f8a361378702",
        statusName: "Регистрация обращений"
    }
};
const userIdCache = new Map();
const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
};

const pool = mysql.createPool(dbConfig);
const onlineUsers = new Set();
const offlineUsers = new Set();

function isTimeInRange(currentTime, startTime, endTime) {
    const current = currentTime.getHours() * 60 + currentTime.getMinutes();
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    const start = startHour * 60 + startMinute;
    const end = endHour * 60 + endMinute;

    if (end < start) {
        // Якщо кінцевий час менший за початковий (перехід через опівніч)
        return current >= start || current < end;
    } else {
        // Звичайний випадок в межах одного дня
        return current >= start && current < end;
    }
}


async function changeUserStatus(userId) {
    try {
        const payload = {
            StatusId: STATUSES.OUTGOING_CALL.id,
            UserId: userId,
            RoleId: "7e2984c7-430b-4b57-abd1-e7976933c7ad",
            ip: 'Auto',
            os: "Майкрософт Windows 10 Корпоративная LTSC"
        };

        await axios.post(
            'https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck',
            payload,
            {
                headers: {
                    "accept": "application/json, text/plain, */*",
                    "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7,uk;q=0.6",
                    "content-type": "application/json"
                },
                httpsAgent
            }
        );

        // console.log(`Статус змінено для користувача з ID: ${userId}`);
    } catch (error) {
        console.error('Помилка зміни статусу:', error.message);
    }
}


async function updateStats(username) {
    try {
        const today = new Date().toLocaleDateString('uk-UA', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('.').reverse().join('-');

        // Оновлюємо щоденну статистику
        await pool.execute(`
            INSERT INTO statistics_daily (username, date, changes_count) 
            VALUES (?, ?, 1) 
            ON DUPLICATE KEY UPDATE 
            changes_count = changes_count + 1
        `, [username, today]);

        // Оновлюємо загальну статистику
        await pool.execute(`
            INSERT INTO statistics_total (username, total_changes) 
            VALUES (?, 1) 
            ON DUPLICATE KEY UPDATE 
            total_changes = total_changes + 1
        `, [username]);

        // Отримуємо оновлені дані для логування
        const [[dailyStats]] = await pool.execute(
            'SELECT changes_count FROM statistics_daily WHERE username = ? AND date = ?',
            [username, today]
        );

        const [[totalStats]] = await pool.execute(
            'SELECT total_changes FROM statistics_total WHERE username = ?',
            [username]
        );

        console.log('debug', `Оновлено статистику для ${username}: за сьогодні (${today}) - ${dailyStats.changes_count}, всього - ${totalStats.total_changes}`);
    } catch (error) {
        console.error('error', `Помилка оновлення статистики для ${username}: ${error.message}`);
    }
}




async function getUserId(xrmUserName) {
    const isCached = userIdCache.has(xrmUserName);

    if (isCached) {
        const userId = userIdCache.get(xrmUserName);
        console.log('debug', `✓ Отримано userId для ${xrmUserName} (Cache): ${userId}`);
        return userId;
    }

    try {
        const userResponse = await axios.get(
            `https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall?filter=${xrmUserName}`,
            { httpsAgent }
        );

        if (!userResponse.data || !userResponse.data[0]) {
            throw new Error('Користувача не знайдено');
        }

        const userId = userResponse.data[0].userId;
        userIdCache.set(xrmUserName, userId);
        console.log('debug', `✓ Отримано userId для ${xrmUserName} (Live): ${userId}`);
        return userId;
    } catch (error) {
        console.error(`Помилка отримання userId для ${xrmUserName}:`, error);
        return null;
    }
}

async function getUserStatus(xrmUserName) {
    try {
        // console.log(`\n-> Перевірка статусу для ${xrmUserName}`);
        const userId = await getUserId(xrmUserName);

        if (!userId) {
            console.log(`❌ Не знайдено userId для ${xrmUserName}`);
            return null;
        }

        const statusResponse = await axios.get(
            `https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId?userId=${userId}`,
            { httpsAgent }
        );

        console.log(`✓ Отримано статус для ${xrmUserName}: ${statusResponse.data.statusName}`);
        if (statusResponse.data.statusName === STATUSES.MANUAL_MODE.statusName) {
            const lastStatusTime = new Date(statusResponse.data.lastStatusTime);
            const currentTime = new Date(statusResponse.data.currentServerTime);//|| new Date());
            const timeDifferenceInMs = currentTime - lastStatusTime;
            const timeDifferenceInSeconds = timeDifferenceInMs / 1000;
            // Перевіряємо, чи користувач був у цьому статусі більше 60 секунд
            if (timeDifferenceInSeconds >= 59) {
                console.log(`⚡ Змінюємо статус для ${xrmUserName}`);
                // sendAudioToClients('/public/assets/notification.mp3');
                await changeUserStatus(userId);
                await updateStats(xrmUserName);
                console.log(`✓ Статус змінено для ${xrmUserName}`);
            } else {
                console.log(`ℹ️ ${xrmUserName} в статусі менше 60 секунд (${timeDifferenceInSeconds.toFixed(1)}с), очікуємо...`);
            }
        }

        return statusResponse.data;
    } catch (error) {
        console.error(`❌ Помилка для ${xrmUserName}:`, error.message);
        return null;
    }
}


async function getBatchUserStatuses(users) {
  try {
    console.log(`🔄 Виконуємо batch запит для ${users.length} користувачів`);

    const response = await axios.get(
      'https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall?filter=%%',
      { httpsAgent }
    );

    if (!response.data || !Array.isArray(response.data)) {
      console.error('❌ Некоректна відповідь від batch API');
      return [];
    }

    console.log(`✓ Отримано ${response.data.length} користувачів з batch API`);

    // Фільтруємо користувачів з targetAudience
    const targetUsers = response.data.filter(user =>
      users.includes(user.userName)
    );

    console.log(`✓ Знайдено ${targetUsers.length} користувачів з цільової аудиторії`);

    // Обробляємо кожного користувача
    for (const user of targetUsers) {
      if (user.statusName === STATUSES.MANUAL_MODE.statusName) {
        console.log(`⚡ Змінюємо статус для ${user.userName} (batch mode)`);
        await changeUserStatus(user.userId);
        await updateStats(user.userName);
        console.log(`✓ Статус змінено для ${user.userName} (batch mode)`);
      } else {
        console.log(`ℹ️ ${user.userName}: ${user.statusName} (batch mode)`);
      }

      // Оновлюємо списки користувачів
      if (user.statusName === STATUSES.OFFLINE.statusName) {
        onlineUsers.delete(user.userName);
        offlineUsers.add(user.userName);
      } else {
        offlineUsers.delete(user.userName);
        onlineUsers.add(user.userName);
      }
    }

    return targetUsers;
  } catch (error) {
    console.error('❌ Помилка batch запиту:', error.message);
    return [];
  }
}

async function processUserGroup(users) {
    console.log(`Починаємо обробку групи користувачів:`, users);
    const promises = users.map(user => getUserStatus(user));
    const results = await Promise.all(promises);

    // Оновлюємо списки на основі результатів
    results.forEach((status, index) => {
        const user = users[index];
        if (status?.statusName === STATUSES.OFFLINE.statusName) {
            onlineUsers.delete(user);
            offlineUsers.add(user);
            console.log(`Користувач ${user} переміщений в офлайн список`);
        } else if (status) {
            offlineUsers.delete(user);
            onlineUsers.add(user);
        }
    });

    return results;
}


async function checkSettings() {
    try {
        // Спочатку створюємо тимчасове підключення без бази даних
        const tempPool = mysql.createPool({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            enableKeepAlive: true,
            keepAliveInitialDelay: 0
        });

        // Створюємо базу даних, якщо не існує
        await tempPool.execute(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME}\``);
        await tempPool.end();

        // Перевіряємо наявність поля batchMode
        const [check] = await pool.execute(`
            SELECT COUNT(*) AS count FROM information_schema.columns 
            WHERE table_schema = ? AND table_name = 'settings' AND column_name = 'batchMode'
        `, [process.env.DB_NAME]);
        
        if (check[0].count === 0) {
            await pool.execute('ALTER TABLE settings ADD COLUMN batchMode BOOLEAN DEFAULT 0');
            console.log('Додано стовпець batchMode до таблиці settings');
        }

        const [rows] = await pool.execute('SELECT * FROM settings LIMIT 1');
        return rows[0];
    } catch (error) {
        console.error('Помилка отримання налаштувань:', error);
        return null;
    }
}
let offlineCheckInterval = null;
async function monitoringLoop() {
    while (true) {
        const settings = await checkSettings();
        if (!settings) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
        }

        const currentTime = new Date();
        let shouldMonitor = false;

        if (settings.startShift === 1 && isTimeInRange(currentTime, '09:00', '09:30')) {
            shouldMonitor = true;
            console.log('Активний моніторинг початку зміни');
        }

        if (settings.endShift === 1 && isTimeInRange(currentTime, '17:25', '17:55')) {
            shouldMonitor = true;
            console.log('Активний моніторинг кінця зміни');
        }

        if (settings.loadEnabled === 1 &&
            settings.timeFrom &&
            settings.timeTo &&
            isTimeInRange(currentTime, settings.timeFrom, settings.timeTo)) {
            shouldMonitor = true;
            console.log('Активний моніторинг по кастомному інтервалу');
        }

        if (shouldMonitor && settings.targetAudience) {
            const users = settings.targetAudience.split(';').filter(Boolean);
            const usersSet = new Set(users);

            // Видаляємо користувачів, яких вже немає в налаштуваннях
            [...onlineUsers, ...offlineUsers].forEach(user => {
                if (!usersSet.has(user)) {
                    console.log(`Видалено користувача: ${user}`);
                    onlineUsers.delete(user);
                    offlineUsers.delete(user);
                }
            });

            // Додаємо нових користувачів
            users.forEach(user => {
                if (!onlineUsers.has(user) && !offlineUsers.has(user)) {
                    console.log(`Додано нового користувача: ${user}`);
                    onlineUsers.add(user);
                }
            });

          console.log(`\n=== Початок нового циклу моніторингу ===`);
          console.log(`Режим оптимізації: ${settings.batchMode === 1 ? 'УВІМКНЕНО' : 'ВИМКНЕНО'}`);
          console.log(`Всього користувачів онлайн: ${onlineUsers.size}`);
          console.log(`Користувачів офлайн: ${offlineUsers.size}`);

          if (settings.batchMode === 1) {
            // Режим оптимізації: один batch запит для всіх користувачів
            console.log(`🚀 Використовуємо режим оптимізації (batch mode)`);
            await getBatchUserStatuses(users);
          } else {
          // Стандартний режим: групова обробка
              const groupSize = 4;
              const activeUsers = Array.from(onlineUsers);
              console.log(`Розмір групи: ${groupSize}`);

              for (let i = 0; i < activeUsers.length; i += groupSize) {
                const userGroup = activeUsers.slice(i, i + groupSize);
                console.log(`\nОбробка групи ${Math.floor(i / groupSize) + 1}:`, userGroup);
                await processUserGroup(userGroup);
                console.log(`Пауза 1 секунда перед наступною групою...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            if (!offlineCheckInterval) {
                offlineCheckInterval = setInterval(async () => {
                    if (offlineUsers.size > 0) {
                        console.log(`\nПеревірка користувачів "Не в системе" (${offlineUsers.size})`);
                        const offlineArray = Array.from(offlineUsers);
                        await processUserGroup(offlineArray);
                    }
                }, 60000);
            }
        } else {
            if (offlineCheckInterval) {
                clearInterval(offlineCheckInterval);
                offlineCheckInterval = null;
            }
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
    }
}



async function startMonitoring() {
    console.log('Monitoring module Active...');
    try {
        await monitoringLoop();
    } catch (error) {
        console.error('Критична помилка моніторингу:', error);
        // Перезапуск моніторингу при критичній помилці
        setTimeout(startMonitoring, 5000);
    }
}

module.exports = {
    startMonitoring
};
