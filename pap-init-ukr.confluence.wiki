h1. Документація API Baby Moderator

h2. Огляд проекту

Baby Moderator - це додаток, розроблений для моніторингу та управління статусами користувачів у системі XRM. Він автоматично змінює статуси користувачів з ручного режиму на режим вихідного дзвінка після визначеного періоду та відстежує ці зміни для статистичних цілей.

h2. Ендпоінти API

{expand:title=Аутентифікація}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/login|POST|Аутентифікує користувача та повертає JWT токен|
|/baby-moderator/api/verify|GET|Перевіряє валідність JWT токена|

h4. Запит на авторизацію
{code:json}
{
  "username": "string",
  "password": "string"
}
{code}

h4. Відповідь на авторизацію
{code:json}
{
  "success": true,
  "token": "JWT_TOKEN_STRING"
}
{code}
{expand}

{expand:title=Управління налаштуваннями}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/dashboard|GET|Отримує поточні налаштування панелі управління|
|/baby-moderator/api/shifts|POST|Оновлює налаштування моніторингу змін|
|/baby-moderator/api/time|POST|Оновлює налаштування користувацького часового інтервалу|
|/baby-moderator/api/audience|POST|Оновлює список цільових користувачів для моніторингу|

h4. Запит налаштувань змін
{code:json}
{
  "startShift": 0/1,
  "endShift": 0/1,
  "loadEnabled": 0/1
}
{code}

h4. Запит налаштувань часу
{code:json}
{
  "timeFrom": "ГГ:ХХ",
  "timeTo": "ГГ:ХХ"
}
{code}

h4. Запит цільової аудиторії
{code:json}
{
  "targetAudience": "username1;username2;username3"
}
{code}
{expand}

{expand:title=Статистика}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/monitoring-stats|GET|Отримує статистику моніторингу за конкретну дату або сьогодні|

h4. Відповідь статистики моніторингу
{code:json}
{
  "daily": {
    "РРРР-ММ-ДД": {
      "username1": 5,
      "username2": 3
    }
  },
  "allTime": {
    "username1": 42,
    "username2": 27
  }
}
{code}
{expand}

{expand:title=Стан системи}
||Ендпоінт||Метод||Опис||
|/baby-moderator/api/test|GET|Тестує підключення додатку та бази даних|

h4. Відповідь тесту
{code:json}
{
  "application": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  },
  "database": {
    "status": "Активний/Неактивний",
    "timestamp": "ISO дата та час"
  }
}
{code}
{expand}

h2. Структура бази даних

Додаток використовує базу даних MariaDB з наступними таблицями:

{expand:title=Таблиця налаштувань (Settings)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|startShift|int(11)|Моніторинг початку зміни (0=вимк, 1=увімк)|
|endShift|int(11)|Моніторинг кінця зміни (0=вимк, 1=увімк)|
|loadEnabled|int(11)|Моніторинг за інтервалом (0=вимк, 1=увімк)|
|timeFrom|text|Моніторинг плаваючого часу, початок інтервалу|
|timeTo|text|Моніторинг плаваючого часу, кінець інтервалу|
|targetAudience|longtext|Список агентів для моніторингу|
{expand}

{expand:title=Таблиця щоденної статистики (Daily Statistics)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|username|varchar(255)|Ім'я користувача для моніторингу|
|date|date|Дата моніторингу|
|changes_count|int(11)|Кількість змін статусу|
|created_at|timestamp|Час створення запису|
{expand}

{expand:title=Таблиця загальної статистики (Total Statistics)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|username|varchar(255)|Ім'я користувача для моніторингу|
|total_changes|int(11)|Загальна кількість змін статусу|
|updated_at|timestamp|Час останнього оновлення|
{expand}

{expand:title=Таблиця користувачів (Users)}
||Колонка||Тип||Опис||
|id|int(11)|Первинний ключ, автоінкремент|
|login|varchar(150)|Логін користувача|
|password|varchar(500)|Зашифрований пароль|
{expand}

h2. Взаємодія із зовнішніми API

Додаток взаємодіє з Kyivstar XRM Gateway API для моніторингу та управління статусами користувачів:

h3. Ендпоінти XRM API, що використовуються

||Ендпоінт||Метод||Призначення||
|https://gateway.xrm.kyivstar.ua/ReferenceBook/SearchUserForCall|GET|Пошук ID користувача за ім'ям користувача|
|https://gateway.xrm.kyivstar.ua/Status/GetLastStatusByUserId|GET|Отримання поточного статусу користувача|
|https://gateway.xrm.kyivstar.ua/Status/ChangeStatusWithCheck|POST|Зміна статусу користувача|

h3. Приклад запиту на зміну статусу XRM

{code:language=json|title=Запит зміни статусу}
{
  "StatusId": "01f6030d-6cb6-4bf7-93c8-8da5a476ed88",
  "UserId": "user-uuid",
  "RoleId": "7e2984c7-430b-4b57-abd1-e7976933c7ad",
  "ip": "Auto",
  "os": "Майкрософт Windows 10 Корпоративная LTSC"
}
{code}

h2. Логіка моніторингу

Додаток реалізує наступну логіку моніторингу:

# Отримує список цільових користувачів з бази даних
# Моніторить користувачів на основі налаштованих часових параметрів:
## Початок зміни (9:00-9:30)
## Кінець зміни (17:25-17:55)
## Користувацький часовий інтервал
# Групує користувачів для обробки (по 4 користувачі за раз)
# Для кожного користувача:
## Отримує ID користувача через XRM API
## Перевіряє поточний статус через XRM API
## Якщо користувач у статусі "Исх - Ручной режим" більше 60 секунд:
### Змінює статус на "Исходящий обзвон"
### Оновлює статистику в базі даних
# Відстежує онлайн та офлайн користувачів
# Перевіряє офлайн користувачів кожні 60 секунд, щоб виявити, коли вони повернуться в онлайн

h2. Системні вимоги

* Node.js
* База даних MariaDB
* Доступ до мережі для роботи з XRM Gateway API
* Налаштування змінних середовища (.env файл)

h2. Інструкції з розгортання

# Клонувати репозиторій
# Встановити залежності: {noformat}npm install{noformat}
# Налаштувати змінні середовища в .env файлі
# Запустити сервер: {noformat}node server.js{noformat}
