const WebSocket = require('ws');
const clients = new Set();

function sendAudioToClients(audioFileName) {
    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'audio',
                file: `${audioFileName}`
            }));
        }
    });
}

// Перевизначення console.log для WebSocket
const originalLog = console.log;
console.log = function () {
    const logMessage = Array.from(arguments).join(' ').replace(/\u001b\[\d+m/g, ''); // Видаляємо ANSI кольорові коди
    originalLog.apply(console, arguments);

    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'log',
                message: logMessage
            }));
        }
    });
};

function initWebSocket(server) {
    const wss = new WebSocket.Server({
        server: server,
        path: '/baby-moderator'
    });
    wss.on('connection', (ws) => {
        clients.add(ws);
        ws.on('close', () => {
            clients.delete(ws);
        });
    });
}

module.exports = {
    initWebSocket,
    sendAudioToClients
};
